/* 通讯录页面现代化头部样式 */
.contacts-header-modern {
    position: relative;
    height: 120px;
    overflow: hidden;
    margin: -20px -20px 0 -20px;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

.header-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        #07C160 0%,
        #00D4AA 25%,
        #00E5B8 50%,
        #1ED760 75%,
        #07C160 100%);
    animation: gradientShift 8s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(135deg, #07C160 0%, #00D4AA 25%, #00E5B8 50%, #1ED760 75%, #07C160 100%);
    }
    50% {
        background: linear-gradient(135deg, #00D4AA 0%, #00E5B8 25%, #1ED760 50%, #07C160 75%, #00D4AA 100%);
    }
}

.header-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(255,255,255,0.15) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255,255,255,0.08) 0%, transparent 30%);
    animation: patternFloat 6s ease-in-out infinite;
}

@keyframes patternFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 1;
    }
    50% {
        transform: translateY(-8px) rotate(2deg);
        opacity: 0.8;
    }
}

.header-content {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
}

.header-main {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-icon-wrapper {
    position: relative;
}

.header-icon-bg {
    width: 56px;
    height: 56px;
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: iconPulse 3s ease-in-out infinite;
}

@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.12),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow:
            0 12px 40px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.4);
    }
}

.header-icon-bg i {
    font-size: 24px;
    color: white;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.header-text {
    color: white;
}

.header-title {
    font-size: 28px;
    font-weight: 700;
    margin: 0 0 4px 0;
    letter-spacing: -0.5px;
    text-shadow: 0 2px 12px rgba(0, 0, 0, 0.2);
    animation: titleSlide 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes titleSlide {
    0% {
        transform: translateX(-30px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.header-subtitle {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    background: rgba(255, 255, 255, 0.15);
    padding: 4px 12px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: inline-block;
    animation: subtitleSlide 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

@keyframes subtitleSlide {
    0% {
        transform: translateX(-20px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.header-actions {
    display: flex;
    gap: 12px;
    animation: actionsSlide 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
    /* 向左移动搜索按钮 */
    margin-right: 20px;
}

@keyframes actionsSlide {
    0% {
        transform: translateX(30px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.action-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    color: white;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 8px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.action-btn:active {
    transform: translateY(0) scale(0.98);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 搜索区域样式 - 高端大厂风格 */
.search-section {
    background: linear-gradient(135deg,
        rgba(7, 193, 96, 0.15) 0%,
        rgba(255, 255, 255, 0.95) 25%,
        rgba(248, 250, 252, 0.98) 100%);
    backdrop-filter: blur(40px) saturate(180%);
    -webkit-backdrop-filter: blur(40px) saturate(180%);
    padding: 28px 24px 24px 24px;
    margin: 0 -20px 20px -20px;
    border-bottom: 1px solid rgba(7, 193, 96, 0.08);
    position: sticky;
    top: 120px;
    z-index: 15;
    box-shadow:
        0 8px 32px rgba(7, 193, 96, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.04),
        0 2px 8px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    display: none;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateY(-10px);
    opacity: 0;
}

.search-section.show {
    transform: translateY(0);
    opacity: 1;
}

.search-container {
    max-width: 100%;
    position: relative;
}

.search-input-wrapper {
    position: relative;
    background: linear-gradient(145deg,
        rgba(255, 255, 255, 0.95) 0%,
        rgba(255, 255, 255, 0.85) 100%);
    border-radius: 20px;
    box-shadow:
        0 8px 24px rgba(7, 193, 96, 0.06),
        0 4px 12px rgba(0, 0, 0, 0.04),
        0 2px 6px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(7, 193, 96, 0.05);
    border: 1.5px solid rgba(7, 193, 96, 0.12);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.search-input-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(7, 193, 96, 0.08) 0%,
        rgba(7, 193, 96, 0.03) 50%,
        rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
    border-radius: 20px;
}

.search-input-wrapper:focus-within {
    box-shadow:
        0 12px 32px rgba(7, 193, 96, 0.15),
        0 6px 16px rgba(7, 193, 96, 0.08),
        0 3px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95),
        inset 0 -1px 0 rgba(7, 193, 96, 0.1);
    border-color: rgba(7, 193, 96, 0.3);
    transform: translateY(-2px) scale(1.02);
}

.search-input-wrapper:focus-within::before {
    opacity: 1;
}

.search-icon-wrapper {
    position: absolute;
    left: 18px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.1) 0%, rgba(7, 193, 96, 0.05) 100%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.search-icon {
    color: #8B9DC3;
    font-size: 16px;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.search-input-wrapper:focus-within .search-icon-wrapper {
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.2) 0%, rgba(7, 193, 96, 0.1) 100%);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 12px rgba(7, 193, 96, 0.2);
}

.search-input-wrapper:focus-within .search-icon {
    color: var(--primary-color);
    transform: scale(1.1);
    filter: drop-shadow(0 2px 4px rgba(7, 193, 96, 0.3));
}

.search-input {
    width: 100%;
    height: 56px;
    border: none;
    background: transparent;
    padding: 0 56px 0 52px;
    font-size: 17px;
    font-weight: 400;
    color: #1a1a1a;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    line-height: 1.6;
    letter-spacing: 0.02em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.search-input:focus {
    outline: none;
    color: #000;
    font-weight: 600;
}

.search-input::placeholder {
    color: #8B9DC3;
    font-size: 16px;
    font-weight: 400;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    letter-spacing: 0.01em;
}

.search-input:focus::placeholder {
    color: #B8C5D1;
    transform: translateX(2px);
}

.search-clear-btn {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    border: none;
    background: linear-gradient(135deg,
        rgba(139, 157, 195, 0.12) 0%,
        rgba(139, 157, 195, 0.06) 100%);
    color: #8B9DC3;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    scale: 0.8;
    box-shadow:
        0 2px 8px rgba(139, 157, 195, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.search-clear-btn.show {
    opacity: 1;
    scale: 1;
}

.search-clear-btn:hover {
    background: linear-gradient(135deg,
        rgba(255, 59, 48, 0.15) 0%,
        rgba(255, 59, 48, 0.08) 100%);
    color: #ff3b30;
    transform: translateY(-50%) scale(1.15);
    box-shadow:
        0 6px 20px rgba(255, 59, 48, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.search-clear-btn:active {
    transform: translateY(-50%) scale(0.95);
    box-shadow:
        0 2px 8px rgba(255, 59, 48, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
}

.search-clear-btn i {
    font-size: 12px;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 搜索结果信息样式 */
.search-results-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.06) 0%, rgba(7, 193, 96, 0.04) 100%);
    border-left: 3px solid var(--primary-color);
    margin: 0 -20px 16px -20px;
    border-radius: 0 8px 8px 0;
}

.results-count {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.2px;
}

.clear-search-btn {
    background: rgba(7, 193, 96, 0.1);
    border: 1px solid rgba(7, 193, 96, 0.2);
    color: var(--primary-color);
    cursor: pointer;
    padding: 6px 12px;
    border-radius: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 12px;
    font-weight: 500;
}

.clear-search-btn:hover {
    background: rgba(7, 193, 96, 0.15);
    border-color: rgba(7, 193, 96, 0.3);
    transform: translateY(-1px);
}

/* 联系人列表样式 */
.contacts-list-container {
    position: relative;
    padding-bottom: 100px;
}

.contacts-list {
    padding: 0;
}



.contact-section {
    margin-bottom: 8px;
}

.section-header {
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.1) 0%, rgba(7, 193, 96, 0.05) 100%);
    backdrop-filter: blur(20px);
    font-size: 13px;
    color: #07C160;
    font-weight: 700;
    position: sticky;
    top: 240px; /* 默认为头部+搜索区域的高度 */
    z-index: 5;
    border-left: 4px solid #07C160;
    border-radius: 0 12px 12px 0;
    margin: 0 0 8px 0;
    letter-spacing: 1px;
    text-transform: uppercase;
    box-shadow: 0 2px 12px rgba(7, 193, 96, 0.15);
    border: 1px solid rgba(7, 193, 96, 0.1);
    transition: top 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 添加位置变化的过渡动画 */
}

.contact-item {
    display: flex;
    padding: 16px 20px;
    align-items: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: relative;
    margin: 4px 0;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.8);
    overflow: hidden;
}

.contact-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(7, 193, 96, 0.1), transparent);
    transition: left 0.5s;
}

.contact-item:hover::before {
    left: 100%;
}

.contact-item:hover {
    background: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border-color: rgba(7, 193, 96, 0.3);
}

.contact-item::after {
    content: '\f054';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    right: 20px;
    color: var(--text-muted);
    font-size: 12px;
    opacity: 0;
    transition: all var(--transition-normal);
}

.contact-item:hover::after {
    opacity: 1;
    color: var(--primary-color);
    transform: translateX(4px);
}

.contact-avatar {
    width: 50px;
    height: 50px;
    border-radius: 14px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    margin-right: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.9);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.contact-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.contact-item:hover .contact-avatar {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.contact-item:hover .contact-avatar::before {
    opacity: 1;
}

.contact-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-name {
    font-size: 16px;
    color: var(--text-primary);
    font-weight: 600;
    line-height: 1.2;
}

.contact-wechat-id {
    font-size: 13px;
    color: var(--text-muted);
    font-weight: 400;
}

.contact-status {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: var(--text-muted);
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--success);
}

.status-dot.offline {
    background: var(--text-muted);
}

/* 字母索引样式 */
.alphabet-index {
    position: fixed;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    gap: 2px;
    z-index: 15;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    padding: 8px 4px;
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.index-letter {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    color: var(--primary-color);
    font-weight: 700;
    cursor: pointer;
    border-radius: 50%;
    transition: all var(--transition-fast);
    position: relative;
}

.index-letter:hover, .index-letter.active {
    background: var(--primary-color);
    color: white;
    transform: scale(1.2);
    box-shadow: 0 2px 8px rgba(7, 193, 96, 0.4);
}

.index-letter::before {
    content: attr(data-letter);
    position: absolute;
    right: 35px;
    background: var(--text-primary);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transform: translateX(10px);
    transition: all var(--transition-fast);
    pointer-events: none;
}

.index-letter:hover::before {
    opacity: 1;
    transform: translateX(0);
}

/* 无搜索结果样式 */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
}

.no-results-icon {
    font-size: 64px;
    color: var(--text-muted);
    margin-bottom: 20px;
    opacity: 0.6;
}

.no-results-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.no-results-subtitle {
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 24px;
    line-height: 1.5;
}



.empty-contacts-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
    font-size: 16px;
    line-height: 1.8;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    margin: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.empty-contacts-message::before {
    content: '👥';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

/* 联系人搜索栏 */
.contacts-search {
    padding: 12px 16px;
    background-color: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: var(--shadow-sm);
    margin-bottom: 15px;
}

.search-input {
    width: 100%;
    height: 40px;
    border-radius: 20px;
    border: none;
    background-color: var(--light-grey);
    padding: 0 40px 0 16px;
    font-size: 15px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    background-color: #f0f0f0;
    box-shadow: inset 0 0 0 1px var(--medium-grey);
}

.search-icon {
    position: absolute;
    right: 30px;
    top: 22px;
    color: var(--dark-grey);
    font-size: 16px;
}

/* 字母索引样式 */
.index-bar {
    position: fixed;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    z-index: 5;
}

.index-item {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--primary-color);
    font-weight: 600;
    cursor: pointer;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.index-item:hover, .index-item.active {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.2);
}

/* 联系人详情卡片 */
.contact-detail-card {
    background-color: #fff;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    margin: 15px;
    overflow: hidden;
    transition: all var(--transition-normal);
}

.contact-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.contact-detail-header {
    background-color: var(--primary-color);
    padding: 20px;
    color: white;
    text-align: center;
}

.contact-detail-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 15px;
    background-size: cover;
    background-position: center;
    border: 3px solid white;
    box-shadow: var(--shadow-md);
}

.contact-detail-name {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
}

.contact-detail-info {
    padding: 20px;
}

.info-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--medium-grey);
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.info-label {
    width: 80px;
    color: var(--dark-grey);
    font-size: 14px;
}

.info-value {
    flex: 1;
    color: var(--text-primary);
    font-weight: 500;
}

/* 移动端样式已合并到下方 */

/* 搜索区域动画 */
@keyframes slideDown {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideUp {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-100%);
        opacity: 0;
    }
}

/* 头部按钮特殊效果 - 精美搜索按钮样式 */
.search-btn {
    position: relative;
    width: 48px !important;
    height: 48px !important;
    border-radius: 16px !important;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.25) 0%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.2) 100%) !important;
    backdrop-filter: blur(25px) saturate(150%) !important;
    -webkit-backdrop-filter: blur(25px) saturate(150%) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.12),
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.4),
        inset 0 -1px 0 rgba(0, 0, 0, 0.05) !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    overflow: hidden;
}

.search-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 100%);
    border-radius: 16px;
    opacity: 0;
    transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    pointer-events: none;
}

.search-btn i {
    font-size: 18px !important;
    color: rgba(255, 255, 255, 0.95) !important;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
    position: relative;
    z-index: 2;
}

.search-btn:hover {
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.35) 0%,
        rgba(255, 255, 255, 0.25) 50%,
        rgba(255, 255, 255, 0.3) 100%) !important;
    transform: translateY(-3px) scale(1.08) !important;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.18),
        0 8px 24px rgba(0, 0, 0, 0.12),
        0 4px 12px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.5),
        inset 0 -1px 0 rgba(0, 0, 0, 0.08) !important;
    border-color: rgba(255, 255, 255, 0.4) !important;
}

.search-btn:hover::before {
    opacity: 1;
}

.search-btn:hover i {
    color: rgba(255, 255, 255, 1) !important;
    transform: scale(1.1) rotate(5deg) !important;
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.25)) !important;
}

.search-btn:active {
    transform: translateY(-1px) scale(1.02) !important;
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.15),
        0 3px 10px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
}

.search-btn:active i {
    transform: scale(0.95) !important;
}

/* 搜索按钮脉动动画 */
@keyframes searchPulse {
    0%, 100% {
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.12),
            0 4px 16px rgba(0, 0, 0, 0.08),
            0 2px 8px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.4),
            inset 0 -1px 0 rgba(0, 0, 0, 0.05),
            0 0 0 0 rgba(255, 255, 255, 0.4);
    }
    50% {
        box-shadow:
            0 8px 32px rgba(0, 0, 0, 0.12),
            0 4px 16px rgba(0, 0, 0, 0.08),
            0 2px 8px rgba(0, 0, 0, 0.04),
            inset 0 1px 0 rgba(255, 255, 255, 0.4),
            inset 0 -1px 0 rgba(0, 0, 0, 0.05),
            0 0 0 8px rgba(255, 255, 255, 0.15);
    }
}

.search-btn {
    animation: searchPulse 3s ease-in-out infinite;
}

/* 搜索按钮图标旋转动画 */
@keyframes searchIconFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-1px) rotate(2deg);
    }
}

.search-btn i {
    animation: searchIconFloat 2s ease-in-out infinite;
}

.add-btn:hover {
    background: rgba(255, 255, 255, 0.35) !important;
}

/* 联系人数量更新动画 */
.header-subtitle {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-subtitle.updating {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.25);
}

/* 响应式设计 - 通讯录页面 */
@media (max-width: 480px) {
    .contacts-header-modern {
        height: 100px;
    }

    .header-content {
        padding: 0 16px;
    }

    .header-icon-bg {
        width: 48px;
        height: 48px;
    }

    .header-icon-bg i {
        font-size: 20px;
    }

    .header-title {
        font-size: 24px;
    }

    .header-subtitle {
        font-size: 12px;
        padding: 3px 10px;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        font-size: 14px;
    }

    .search-section {
        top: 100px;
        padding: 12px 16px 16px 16px;
    }

    .section-header {
        top: 180px;
        padding: 10px 16px;
        font-size: 12px;
    }

    /* 移动端联系人列表容器调整 */
    .contacts-list-container {
        padding-top: 120px; /* 为头部留出空间 */
    }

    /* 移动端添加好友按钮调整 */
    .add-friend-btn {
        padding: 6px 12px;
        font-size: 11px;
    }

    /* 移动端联系人操作区域 */
    .contact-actions {
        padding-left: 8px;
    }

    /* 移动端搜索结果信息 */
    .search-results-info {
        padding: 8px 16px;
        font-size: 13px;
    }

    .contact-item {
        padding: 14px 16px;
        margin: 3px 0;
        border-radius: 12px;
    }
}

/* 联系人操作按钮区域 */
.contact-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
    padding-left: 12px;
}

/* 添加好友按钮 */
.add-friend-btn {
    background: linear-gradient(135deg, #07C160, #00D4AA);
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(7, 193, 96, 0.3);
    white-space: nowrap;
}

.add-friend-btn:hover {
    background: linear-gradient(135deg, #06B050, #00C49A);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(7, 193, 96, 0.4);
}

.add-friend-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(7, 193, 96, 0.3);
}

.add-friend-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 已添加状态标签 */
.friend-status.added {
    background: rgba(7, 193, 96, 0.1);
    color: #07C160;
    border: 1px solid rgba(7, 193, 96, 0.3);
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

/* 删除好友右键菜单 */
.delete-friend-menu {
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    animation: menuFadeIn 0.2s ease-out;
}

@keyframes menuFadeIn {
    from {
        opacity: 0;
        transform: scale(0.95) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.delete-friend-menu .menu-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.delete-friend-menu .menu-item:hover {
    background-color: #f5f5f5;
}

.delete-friend-menu .menu-item i {
    width: 16px;
    text-align: center;
}

/* 删除好友确认弹窗 - 大厂风格设计 */
.friend-delete-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(24px) saturate(180%);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInBackdrop 0.4s ease-out forwards;
}

@keyframes friendDeleteFadeInBackdrop {
    from {
        opacity: 0;
        backdrop-filter: blur(0px) saturate(100%);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(24px) saturate(180%);
    }
}

.friend-delete-confirm-modal.active {
    display: flex;
    opacity: 1;
}

.friend-delete-confirm-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(32px) saturate(200%);
    border-radius: 28px;
    width: 90%;
    max-width: 420px;
    box-shadow:
        0 32px 80px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(40px) scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.6);
}

.friend-delete-confirm-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(0, 0, 0, 0.02) 100%);
    z-index: -1;
}

.friend-delete-confirm-modal.active .friend-delete-confirm-container {
    transform: translateY(0) scale(1);
    animation: friendDeleteSlideInScale 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.friend-delete-confirm-modal.closing {
    opacity: 0;
    transition: opacity 0.25s cubic-bezier(0.4, 0, 0.6, 1);
}

.friend-delete-confirm-modal.closing .friend-delete-confirm-container {
    transform: translateY(15px) scale(0.96);
    opacity: 0;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.6, 1);
}

@keyframes friendDeleteSlideInScale {
    0% {
        transform: translateY(40px) scale(0.9);
        opacity: 0;
    }
    60% {
        transform: translateY(-8px) scale(1.02);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.friend-delete-confirm-content {
    text-align: center;
    padding: 48px 32px 32px;
    position: relative;
}

.friend-delete-confirm-icon {
    position: relative;
    margin-bottom: 32px;
    display: inline-block;
}

.friend-delete-confirm-icon i {
    font-size: 72px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 8px 16px rgba(239, 68, 68, 0.3));
    animation: friendDeletePulseWarning 2s ease-in-out infinite;
    position: relative;
}

@keyframes friendDeletePulseWarning {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 8px 16px rgba(239, 68, 68, 0.3));
    }
    50% {
        transform: scale(1.05);
        filter: drop-shadow(0 12px 24px rgba(239, 68, 68, 0.4));
    }
}

.friend-delete-confirm-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(239, 68, 68, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: friendDeleteRippleWarning 2s ease-in-out infinite;
}

@keyframes friendDeleteRippleWarning {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.2;
    }
}

.friend-delete-confirm-title {
    font-size: 28px;
    font-weight: 700;
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    letter-spacing: -0.5px;
    line-height: 1.2;
}

.friend-delete-confirm-message {
    font-size: 18px;
    color: #6b7280;
    line-height: 1.6;
    margin-bottom: 40px;
    font-weight: 400;
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;
}

#friend-delete-name {
    font-weight: 600;
    color: #374151;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.friend-delete-confirm-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    align-items: center;
}

.friend-delete-cancel-btn, .friend-delete-confirm-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    min-width: 120px;
    justify-content: center;
    letter-spacing: 0.5px;
}

.friend-delete-cancel-btn {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #64748b;
    border: 2px solid rgba(148, 163, 184, 0.3);
    box-shadow:
        0 4px 12px rgba(148, 163, 184, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.friend-delete-cancel-btn:hover {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #475569;
    transform: translateY(-2px);
    box-shadow:
        0 8px 24px rgba(148, 163, 184, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.friend-delete-confirm-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    color: white;
    box-shadow:
        0 8px 24px rgba(239, 68, 68, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(239, 68, 68, 0.3);
}

.friend-delete-confirm-btn:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    transform: translateY(-2px);
    box-shadow:
        0 12px 32px rgba(239, 68, 68, 0.45),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.friend-delete-cancel-btn:active, .friend-delete-confirm-btn:active {
    transform: translateY(0) scale(0.98);
    transition: all 0.15s ease-out;
}

/* 按钮点击后的状态 */
.friend-delete-cancel-btn.clicked {
    transform: scale(0.98);
    opacity: 0.8;
    transition: all 0.15s ease-out;
}

.friend-delete-confirm-btn.clicked {
    transform: scale(0.98);
    opacity: 0.8;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    transition: all 0.15s ease-out;
}

/* 删除好友确认弹窗响应式设计 */
@media (max-width: 768px) {
    .friend-delete-confirm-container {
        width: 95%;
        max-width: 360px;
        margin: 20px;
        border-radius: 24px;
    }

    .friend-delete-confirm-content {
        padding: 40px 24px 28px;
    }

    .friend-delete-confirm-icon i {
        font-size: 64px;
    }

    .friend-delete-confirm-title {
        font-size: 24px;
        margin-bottom: 12px;
    }

    .friend-delete-confirm-message {
        font-size: 16px;
        margin-bottom: 32px;
    }

    .friend-delete-confirm-actions {
        flex-direction: column;
        gap: 12px;
    }

    .friend-delete-cancel-btn, .friend-delete-confirm-btn {
        width: 100%;
        justify-content: center;
        padding: 16px 24px;
        font-size: 16px;
    }
}

/* 空状态样式 */
.empty-contacts-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
    color: #999;
}

.empty-contacts-message .empty-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 16px;
}

.empty-contacts-message .empty-title {
    font-size: 18px;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
}

.empty-contacts-message .empty-subtitle {
    font-size: 14px;
    color: #999;
    line-height: 1.5;
}

.empty-contacts-message .empty-subtitle i {
    color: #07C160;
}