/* 聊天列表样式 */
.chat-list {
    padding: 0;
}



/* 空聊天提示 */
.empty-chat-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
    font-size: 16px;
    line-height: 1.8;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    margin: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.empty-chat-message::before {
    content: '💬';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.chat-item {
    display: flex;
    padding: 18px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    position: relative;
    transition: all var(--transition-fast);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    margin: 8px 12px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.chat-item:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-md);
}

.chat-avatar {
    width: 54px;
    height: 54px;
    border-radius: var(--border-radius-md);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: cover;
    background-position: center;
    margin-right: 16px;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all var(--transition-fast);
}

.chat-item:hover .chat-avatar {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.chat-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
}

.chat-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
}

.chat-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.chat-time {
    font-size: 12px;
    color: var(--dark-grey);
    margin-left: 10px;
    flex-shrink: 0;
}

.chat-message {
    font-size: 14px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
    line-height: 1.4;
}

.chat-badge {
    background-color: var(--red);
    color: white;
    font-size: 12px;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
    box-shadow: 0 2px 5px rgba(255, 59, 48, 0.3);
    font-weight: 600;
    position: absolute;
    top: 6px;
    left: 40px;
    animation: badgePulse 2s infinite;
    transform-origin: center;
    z-index: 5;
}

@keyframes badgePulse {
    0% {
        transform: scale(1);
    }
    10% {
        transform: scale(1.1);
    }
    20% {
        transform: scale(1);
    }
    100% {
        transform: scale(1);
    }
}

/* 聊天详情页样式 */
.chat-detail {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 200;
    max-width: 600px;
    margin: 0 auto;
    display: none;
    flex-direction: column;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.chat-detail.active {
    display: flex;
    opacity: 1;
    transform: translateY(0);
}

.chat-header {
    height: 60px;
    border-bottom: 1px solid var(--medium-grey);
    display: flex;
    align-items: center;
    padding: 0 15px;
    background-color: #fff;
    box-shadow: var(--shadow-sm);
}

.back-btn, .more-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-primary);
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.back-btn:hover, .more-btn:hover {
    background-color: var(--light-grey);
}

.chat-header h3 {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 15px;
    background-color: var(--light-grey);
    background-image: 
        radial-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
        radial-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
    scroll-behavior: smooth;
}

/* 加载提示和错误提示 */
.loading-messages, .error-message, .empty-messages {
    text-align: center;
    padding: 30px 20px;
    color: var(--dark-grey);
    font-size: 15px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius-md);
    margin: 20px 0;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: var(--shadow-sm);
}

.error-message {
    color: var(--red);
    background-color: rgba(255, 59, 48, 0.05);
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 20px;
    align-items: flex-start;
    position: relative;
    transition: transform 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #eee;
    background-size: cover;
    background-position: center;
    margin-right: 12px;
    box-shadow: var(--shadow-sm);
    border: 2px solid #fff;
}

.message.sent {
    flex-direction: row-reverse;
}

.message.sent .message-avatar {
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    max-width: 70%;
    position: relative;
}

.message-bubble {
    padding: 14px 18px;
    font-size: 16px;
    word-break: break-word;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    line-height: 1.5;
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.message.received .message-bubble {
    background: linear-gradient(135deg,
        rgba(224, 244, 255, 0.98) 0%,
        rgba(179, 229, 255, 0.95) 50%,
        rgba(135, 206, 235, 0.92) 100%);
    border-radius: 20px 20px 20px 6px;
    color: #1e293b;
    border: 1px solid rgba(135, 206, 235, 0.3);
    box-shadow:
        0 4px 12px rgba(135, 206, 235, 0.15),
        0 2px 6px rgba(135, 206, 235, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
}

.message.received .message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -9px;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg,
        rgba(179, 229, 255, 0.95) 0%,
        rgba(135, 206, 235, 0.92) 100%);
    border-radius: 0 0 18px 0;
    z-index: -1;
    border-right: 1px solid rgba(135, 206, 235, 0.3);
    border-bottom: 1px solid rgba(135, 206, 235, 0.3);
}

/* 添加好友消息气泡的微妙光泽效果 */
.message.received .message-bubble::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%);
    border-radius: 20px 20px 0 0;
    pointer-events: none;
}

/* 好友消息悬浮效果 */
.message.received:hover .message-bubble {
    background: linear-gradient(135deg,
        rgba(224, 244, 255, 1) 0%,
        rgba(179, 229, 255, 0.98) 50%,
        rgba(135, 206, 235, 0.95) 100%);
    box-shadow:
        0 6px 20px rgba(135, 206, 235, 0.25),
        0 3px 10px rgba(135, 206, 235, 0.15),
        0 1px 4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 好友消息文字样式优化 */
.message.received .message-bubble {
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 1.5;
}

/* 好友消息时间戳样式 */
.message.received .message-time {
    color: #64748b;
    font-size: 12px;
    opacity: 0.8;
}

.message.sent .message-bubble {
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px 20px 6px 20px;
    box-shadow: var(--shadow-md), 0 0 20px rgba(7, 193, 96, 0.2);
}

.message.sent .message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: -9px;
    width: 18px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 0 0 0 18px;
    z-index: -1;
}

.message-time {
    font-size: 12px;
    color: var(--dark-grey);
    margin-top: 5px;
    text-align: right;
    opacity: 0.8;
}

.message.received .message-time {
    text-align: left;
}

/* 未读消息提示条 */
.unread-indicator {
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(7, 193, 96, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    padding: 10px 18px;
    border-radius: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 150;
    animation: slideUpFadeIn 0.3s ease-out;
    font-size: 14px;
    font-weight: 500;
    min-width: 140px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.unread-indicator:hover {
    background: rgba(7, 193, 96, 1);
    transform: translateX(-50%) translateY(-3px);
    box-shadow: 0 8px 25px rgba(7, 193, 96, 0.5);
}

.unread-indicator:active {
    transform: translateX(-50%) translateY(-1px);
    box-shadow: 0 4px 15px rgba(7, 193, 96, 0.4);
}

.unread-indicator-content {
    display: flex;
    align-items: center;
    gap: 4px;
}

.unread-count {
    font-weight: 600;
    font-size: 15px;
}

.unread-text {
    font-size: 13px;
    opacity: 0.9;
}

.unread-scroll-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.unread-scroll-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.unread-scroll-btn i {
    font-size: 12px;
}

@keyframes slideUpFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 输入区域样式 - 现代化大厂风格 */
.chat-input-area {
    padding: 20px 16px 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    flex-direction: column;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.08),
        0 -4px 16px rgba(0, 0, 0, 0.04),
        0 -2px 8px rgba(0, 0, 0, 0.02);
    border-radius: 20px 20px 0 0;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-bottom: none;
    position: relative;
}

/* 功能按钮栏 */
.chat-function-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    padding: 0 4px;
    justify-content: flex-start;
    align-items: center;
}

.function-btn {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06),
        0 1px 3px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    border: none;
    flex-shrink: 0;
}

/* 表情按钮 - 默认橙色 */
.emoji-btn {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
}

.emoji-btn:hover {
    background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(255, 152, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 图片按钮 - 默认紫色 */
.media-btn {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    color: white;
}

.media-btn:hover {
    background: linear-gradient(135deg, #ba68c8 0%, #9c27b0 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(156, 39, 176, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 文件按钮 - 默认绿色 */
.file-btn {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.file-btn:hover {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(76, 175, 80, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

.function-btn:active {
    transform: translateY(-1px) scale(1.05);
    transition: all 0.1s ease;
}

/* 添加按钮内部光晕效果 */
.function-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.function-btn:hover::before {
    width: 40px;
    height: 40px;
    opacity: 1;
}

/* 图标统一样式和动画 */
.function-btn i {
    font-size: 20px !important;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.function-btn:hover i {
    transform: scale(1.15);
}

/* 输入框容器 */
.chat-input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.input-wrapper {
    flex: 1;
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
    border-radius: 24px;
    border: 2px solid transparent;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

/* 渐变边框效果 */
.input-wrapper::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(7, 193, 96, 0.3) 0%,
        rgba(76, 175, 80, 0.3) 50%,
        rgba(156, 39, 176, 0.3) 100%);
    border-radius: 26px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.input-wrapper:focus-within::before {
    opacity: 1;
}

.input-wrapper:focus-within {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(7, 193, 96, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

#chat-input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 16px 20px;
    font-size: 16px;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

#chat-input::-webkit-scrollbar {
    display: none;
}

#chat-input::placeholder {
    color: #999;
    font-weight: 400;
}



/* 发送按钮 */
.send-button {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 6px 20px rgba(7, 193, 96, 0.3),
        0 3px 10px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.send-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.send-button:hover::before {
    opacity: 1;
}

.send-button:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow:
        0 8px 30px rgba(7, 193, 96, 0.4),
        0 4px 15px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

.send-button:active {
    transform: scale(0.95) translateY(0);
    transition: all 0.1s ease;
}

.send-button:disabled {
    background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
    color: #999;
    cursor: not-allowed;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.06);
}

.send-button:disabled:hover {
    transform: none;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.06);
}

.send-button i {
    font-size: 18px;
    margin-left: 2px;
}

/* 图片和视频消息样式增强 */
.image-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.image-container:hover {
    transform: scale(1.03);
}

.message-image {
    max-width: 240px;
    max-height: 300px;
    border-radius: 12px;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    border: 2px solid #fff;
    display: block;
}

.video-container {
    position: relative;
    display: inline-block;
    transition: all var(--transition-fast);
}

.video-container:hover {
    transform: scale(1.03);
}

.message-video {
    max-width: 240px;
    max-height: 300px;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    border: 2px solid #fff;
    display: block;
}

/* 视频全屏按钮样式 */
.video-fullscreen-overlay,
.video-fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.video-fullscreen-overlay:hover,
.video-fullscreen-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.video-fullscreen-overlay i,
.video-fullscreen-btn i {
    color: white;
    font-size: 16px;
}

/* 文件消息样式 */
.file-message {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 0;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.file-message:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.file-info {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
    position: relative;
}

.file-info.uploading {
    opacity: 0.7;
}

.file-info.uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-download {
    margin-left: auto;
    padding: 8px;
    border-radius: 50%;
    background: rgba(7, 193, 96, 0.1);
    transition: all var(--transition-fast);
}

.file-download:hover {
    background: rgba(7, 193, 96, 0.2);
    transform: scale(1.1);
}

.file-download a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 16px;
}

/* 上传中的媒体样式 */
.message-image.uploading,
.message-video.uploading {
    opacity: 0.7;
    position: relative;
}

.message-image.uploading::after,
.message-video.uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 消息状态样式 */
.message-status.uploading {
    color: #ffa500;
}

.message-status.uploading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.file-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 13px;
    color: var(--text-secondary);
    opacity: 0.8;
}

/* 表情选择器 - 现代化设计 */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 20px;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.12),
        0 -4px 16px rgba(0, 0, 0, 0.08),
        0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    display: none;
    border: 2px solid transparent;
    margin-bottom: 8px;
    max-height: 350px;
    overflow: hidden;
}

.emoji-picker.active {
    display: block;
}

/* 表情选择器头部 - 隐藏分类按钮 */
.emoji-picker-header {
    display: none;
}

.emoji-categories {
    display: flex;
    gap: 8px;
    justify-content: space-around;
}

.emoji-category {
    width: 36px;
    height: 36px;
    border-radius: 12px;
    background: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    transition: all 0.3s ease;
    position: relative;
}

.emoji-category:hover {
    background: rgba(7, 193, 96, 0.1);
    color: #07c160;
    transform: scale(1.1);
}

.emoji-category.active {
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

/* 表情内容区域 */
.emoji-picker-content {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    max-height: 240px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(7, 193, 96, 0.3) transparent;
}

.emoji-picker-content::-webkit-scrollbar {
    width: 6px;
}

.emoji-picker-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.emoji-picker-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.emoji-picker-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #06a84d 0%, #43a047 100%);
}

.emoji-item {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    position: relative;
    background: transparent;
}

.emoji-item::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.4) 0%,
        rgba(255, 152, 0, 0.4) 50%,
        rgba(156, 39, 176, 0.4) 100%);
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.emoji-item:hover::before {
    opacity: 1;
}

.emoji-item:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    transform: translateY(-2px) scale(1.2);
    box-shadow:
        0 8px 20px rgba(243, 156, 18, 0.2),
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06);
}

.emoji-item:active {
    transform: translateY(0) scale(1.1);
    transition: all 0.1s ease;
}

/* 评论栏按钮样式 */
.comment-bar-actions {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.emoji-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 5px 10px;
    opacity: 0.7;
    transition: opacity 0.2s;
    border-radius: 4px;
}

.emoji-btn:hover {
    opacity: 1;
    background-color: #f5f5f5;
}

.send-btn, .cancel-btn {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    margin-left: 8px;
}

.send-btn {
    background-color: #07c160;
    color: white;
    box-shadow: 0 2px 4px rgba(7, 193, 96, 0.3);
}

.send-btn:hover:not([disabled]) {
    background-color: #06b457;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(7, 193, 96, 0.4);
}

.send-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

.cancel-btn {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.cancel-btn:hover {
    background-color: #eee;
    color: #333;
}

/* 媒体预览弹窗 */
.media-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    backdrop-filter: blur(4px);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.media-preview-content {
    max-width: 95%;
    max-height: 95%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.media-preview-content img,
.media-preview-content video {
    max-width: 100%;
    max-height: 90vh;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    object-fit: contain;
}

.media-close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100000;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    color: white;
    font-size: 40px;
    font-weight: normal;
    line-height: 1;
}

.media-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}



.empty-message {
    text-align: center;
    padding: 30px;
    color: #999;
    font-size: 14px;
}

/* 聊天详情页面的表情选择器样式 */
.chat-detail .emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 20px;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.12),
        0 -4px 16px rgba(0, 0, 0, 0.08),
        0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    display: none;
    border: 2px solid transparent;
    margin-bottom: 8px;
    max-height: 350px;
    overflow: hidden;
}

.chat-detail .emoji-picker.active {
    display: block !important;
}

/* 修复表情选择器定位问题 */
#emoji-picker.active,
.chat-detail .emoji-picker.active {
    display: block !important;
    /* 修复定位 */
    position: absolute !important;
    bottom: 100% !important;
    left: 0 !important;
    right: 0 !important;
    max-width: 100% !important;
    margin-bottom: 8px !important;
    z-index: 1000 !important;
}

/* 确保在所有情况下都隐藏表情分类头部 */
.emoji-picker-header,
.chat-detail .emoji-picker-header,
#emoji-picker .emoji-picker-header {
    display: none !important;
}

/* 聊天右键菜单样式 */
.chat-context-menu {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 10000;
    min-width: 140px;
    overflow: hidden;
    animation: contextMenuShow 0.2s ease-out;
}

.chat-context-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-primary);
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chat-context-menu-item:last-child {
    border-bottom: none;
}

.chat-context-menu-item:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.chat-context-menu-item i {
    margin-right: 8px;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

@keyframes contextMenuShow {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ==================== WebSocket 相关样式 ==================== */

/* 输入状态指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 8px 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 18px;
    max-width: 200px;
    animation: fadeInUp 0.3s ease-out;
}

.typing-dots {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    margin: 0 2px;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-text {
    font-size: 12px;
    color: var(--text-muted);
    font-style: italic;
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* WebSocket连接状态指示器 */
.websocket-status {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.websocket-status.connected {
    background: rgba(76, 175, 80, 0.9);
    color: white;
}

.websocket-status.disconnected {
    background: rgba(244, 67, 54, 0.9);
    color: white;
}

.websocket-status.connecting {
    background: rgba(255, 193, 7, 0.9);
    color: white;
}

/* 在线状态指示器 */
.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background: #4CAF50;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}

.online-indicator.offline {
    background: #9E9E9E;
}

/* 消息状态增强样式 */
.message-status.read {
    color: #4CAF50;
}

.message-status.sent {
    color: #9E9E9E;
}

.message-status.sending {
    color: #FF9800;
}

.message-status.failed {
    color: #F44336;
    cursor: pointer;
}


