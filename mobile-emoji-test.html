<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端表情选择器测试</title>
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/moments.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .device-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #666;
        }
        
        .test-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-bottom: 20px;
            transition: transform 0.2s ease;
        }
        
        .test-button:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">移动端表情选择器测试</h1>
        
        <div class="device-info">
            <div>屏幕宽度: <span id="screen-width"></span>px</div>
            <div>设备类型: <span id="device-type"></span></div>
            <div>触摸支持: <span id="touch-support"></span></div>
        </div>
        
        <button class="test-button" onclick="openCommentModal()">
            测试评论表情选择器
        </button>
        
        <!-- 评论弹窗 -->
        <div class="comment-modal" id="moment-comment-modal">
            <div class="comment-container">
                <div class="comment-content-wrapper">
                    <textarea id="moment-comment-content" placeholder="在这里测试表情输入..."></textarea>
                    <!-- 评论表情选择器 -->
                    <div class="emoji-picker" id="moment-comment-emoji-picker">
                        <div class="emoji-container">
                            <!-- 笑脸表情 -->
                            <div class="emoji">😀</div>
                            <div class="emoji">😁</div>
                            <div class="emoji">😂</div>
                            <div class="emoji">🤣</div>
                            <div class="emoji">😊</div>
                            <div class="emoji">😍</div>
                            <div class="emoji">🥰</div>
                            <div class="emoji">😘</div>
                            <div class="emoji">😗</div>
                            <div class="emoji">😙</div>
                            <div class="emoji">😚</div>
                            <div class="emoji">🤗</div>
                            <div class="emoji">🤔</div>
                            <div class="emoji">🤨</div>
                            <div class="emoji">😏</div>
                            <div class="emoji">😮</div>
                            <div class="emoji">😱</div>
                            <div class="emoji">😭</div>
                            <div class="emoji">😤</div>
                            <div class="emoji">🥳</div>
                            <div class="emoji">😎</div>
                            <div class="emoji">🤓</div>
                            <div class="emoji">🧐</div>
                            <div class="emoji">😴</div>
                            <!-- 手势表情 -->
                            <div class="emoji">👍</div>
                            <div class="emoji">👎</div>
                            <div class="emoji">👌</div>
                            <div class="emoji">✌️</div>
                            <div class="emoji">🤞</div>
                            <div class="emoji">🤟</div>
                            <div class="emoji">🤘</div>
                            <div class="emoji">👏</div>
                            <div class="emoji">🙌</div>
                            <div class="emoji">👐</div>
                            <div class="emoji">🤲</div>
                            <div class="emoji">🙏</div>
                            <!-- 爱心表情 -->
                            <div class="emoji">❤️</div>
                            <div class="emoji">💕</div>
                            <div class="emoji">💖</div>
                            <div class="emoji">💗</div>
                            <div class="emoji">💙</div>
                            <div class="emoji">💚</div>
                            <div class="emoji">💛</div>
                            <div class="emoji">🧡</div>
                            <div class="emoji">💜</div>
                            <div class="emoji">🖤</div>
                            <div class="emoji">🤍</div>
                            <div class="emoji">🤎</div>
                            <!-- 自然表情 -->
                            <div class="emoji">🌹</div>
                            <div class="emoji">🌸</div>
                            <div class="emoji">🌺</div>
                            <div class="emoji">🌻</div>
                            <div class="emoji">🌞</div>
                            <div class="emoji">🌝</div>
                            <div class="emoji">🌛</div>
                            <div class="emoji">⭐</div>
                            <div class="emoji">🌟</div>
                            <div class="emoji">✨</div>
                            <div class="emoji">🌈</div>
                            <div class="emoji">☀️</div>
                            <!-- 食物表情 -->
                            <div class="emoji">🍎</div>
                            <div class="emoji">🍊</div>
                            <div class="emoji">🍋</div>
                            <div class="emoji">🍌</div>
                            <div class="emoji">🍉</div>
                            <div class="emoji">🍇</div>
                            <div class="emoji">🍓</div>
                            <div class="emoji">🥝</div>
                            <div class="emoji">🍑</div>
                            <div class="emoji">🥭</div>
                            <div class="emoji">🍍</div>
                            <div class="emoji">🥥</div>
                        </div>
                    </div>
                </div>
                <div class="comment-actions">
                    <div class="emoji-btn" id="moment-comment-emoji-btn">
                        <i class="fas fa-smile"></i>
                    </div>
                    <div class="comment-btns">
                        <button id="moment-cancel-comment">取消</button>
                        <button id="moment-submit-comment">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/moments.js"></script>
    <script>
        // 显示设备信息
        document.getElementById('screen-width').textContent = window.innerWidth;
        
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        document.getElementById('device-type').textContent = isMobile ? '移动设备' : '桌面设备';
        
        const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
        document.getElementById('touch-support').textContent = hasTouch ? '支持' : '不支持';
        
        // 打开评论弹窗
        function openCommentModal() {
            const modal = document.getElementById('moment-comment-modal');
            modal.style.display = 'flex';
            modal.classList.add('active');
            
            // 初始化表情选择器
            if (typeof initCommentEmojiPicker === 'function') {
                initCommentEmojiPicker();
            } else {
                // 简单的表情选择器初始化
                initSimpleEmojiPicker();
            }
        }

        // 简单的表情选择器初始化
        function initSimpleEmojiPicker() {
            const emojiPicker = document.getElementById('moment-comment-emoji-picker');
            const emojiContainer = emojiPicker.querySelector('.emoji-container');
            const textInput = document.getElementById('moment-comment-content');

            // 为每个表情添加点击事件
            emojiContainer.querySelectorAll('.emoji').forEach(emoji => {
                emoji.addEventListener('click', function() {
                    // 插入表情到输入框
                    const emojiText = this.textContent;
                    const currentValue = textInput.value;
                    const cursorPos = textInput.selectionStart || currentValue.length;

                    const newValue = currentValue.slice(0, cursorPos) + emojiText + currentValue.slice(cursorPos);
                    textInput.value = newValue;

                    // 设置光标位置
                    textInput.focus();
                    textInput.setSelectionRange(cursorPos + emojiText.length, cursorPos + emojiText.length);

                    // 隐藏表情选择器
                    emojiPicker.classList.remove('active');
                });
            });
        }

        // 切换表情选择器显示
        function toggleEmojiPicker(pickerId, event) {
            event.stopPropagation();
            const picker = document.getElementById(pickerId);

            if (picker.classList.contains('active')) {
                picker.classList.remove('active');
            } else {
                // 隐藏其他表情选择器
                document.querySelectorAll('.emoji-picker.active').forEach(p => {
                    p.classList.remove('active');
                });

                picker.classList.add('active');

                // 优化移动端位置
                if (window.innerWidth <= 768) {
                    optimizeEmojiPickerPosition(picker);
                }
            }
        }

        // 优化表情选择器位置
        function optimizeEmojiPickerPosition(picker) {
            requestAnimationFrame(() => {
                const pickerRect = picker.getBoundingClientRect();
                const viewportHeight = window.innerHeight;
                const viewportWidth = window.innerWidth;

                // 检查是否超出屏幕底部
                if (pickerRect.bottom > viewportHeight) {
                    picker.style.top = 'auto';
                    picker.style.bottom = '100%';
                    picker.style.marginTop = '0';
                    picker.style.marginBottom = '8px';
                }

                // 检查是否超出屏幕右侧
                if (pickerRect.right > viewportWidth) {
                    picker.style.right = '0';
                    picker.style.left = 'auto';
                }
            });
        }

        // 关闭评论弹窗
        function closeCommentModal() {
            const modal = document.getElementById('moment-comment-modal');
            modal.classList.remove('active');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }
        
        // 绑定事件
        document.getElementById('moment-cancel-comment').addEventListener('click', closeCommentModal);
        document.getElementById('moment-submit-comment').addEventListener('click', closeCommentModal);
        
        // 点击背景关闭弹窗
        document.getElementById('moment-comment-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCommentModal();
            }
        });
        
        // 表情按钮事件
        document.getElementById('moment-comment-emoji-btn').addEventListener('click', function(e) {
            toggleEmojiPicker('moment-comment-emoji-picker', e);
        });
        
        // 窗口大小改变时更新信息
        window.addEventListener('resize', function() {
            document.getElementById('screen-width').textContent = window.innerWidth;
        });
    </script>
</body>
</html>
