<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信</title>
    <!-- 公共样式 -->
    <link rel="stylesheet" href="css/common.css">
    <!-- 各个页面的样式 -->
    <link rel="stylesheet" href="css/chat.css">
    <link rel="stylesheet" href="css/contacts.css">
    <link rel="stylesheet" href="css/moments.css">
    <link rel="stylesheet" href="css/profile.css">
    <link rel="stylesheet" href="css/albums.css">
    <!-- 字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 聊天页面 -->
            <div class="page active" id="chat-page">
                <!-- 通过JS动态加载页面内容 -->
            </div>

            <!-- 通讯录页面 -->
            <div class="page" id="contacts-page">
                <!-- 通过JS动态加载页面内容 -->
            </div>

            <!-- 发现页面（朋友圈） -->
            <div class="page" id="moments-page">
                <!-- 通过JS动态加载页面内容 -->
            </div>

            <!-- 我的页面 -->
            <div class="page" id="profile-page">
                <!-- 通过JS动态加载页面内容 -->
            </div>

            <!-- 相册页面 -->
            <div class="page" id="albums-page">
                <!-- 通过JS动态加载页面内容 -->
            </div>

            <!-- 相册详情页面 -->
            <div class="page" id="album-detail-page">
                <!-- 通过JS动态加载页面内容 -->
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <div class="tab-item active" data-page="chat-page">
                <div class="tab-icon-container">
                    <i class="fas fa-comment"></i>
                    <div class="tab-badge" id="chat-tab-badge" style="display: none;">0</div>
                </div>
                <span>聊天</span>
            </div>
            <div class="tab-item" data-page="contacts-page">
                <i class="fas fa-address-book"></i>
                <span>通讯录</span>
            </div>
            <div class="tab-item" data-page="moments-page">
                <i class="fas fa-compass"></i>
                <span>发现</span>
            </div>
            <div class="tab-item" data-page="profile-page">
                <i class="fas fa-user"></i>
                <span>我</span>
            </div>
        </div>
    </div>

    <!-- 图片查看器弹窗 -->
    <div class="media-viewer-modal" id="image-viewer-modal">
        <div class="media-viewer-container">
            <button id="close-image-viewer" class="close-media-btn"><i class="fas fa-times"></i></button>
            <div class="image-viewer-content">
                <div class="image-container" id="image-container">
                    <!-- 图片将通过JavaScript动态加载 -->
                </div>
                <div class="image-navigation">
                    <button id="prev-image" class="nav-btn"><i class="fas fa-chevron-left"></i></button>
                    <div id="image-counter" class="image-counter">1/1</div>
                    <button id="next-image" class="nav-btn"><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        </div>
    </div>

    <!-- 视频查看器弹窗 -->
    <div class="media-viewer-modal" id="video-viewer-modal">
        <div class="media-viewer-container">
            <button id="close-video-viewer" class="close-media-btn"><i class="fas fa-times"></i></button>
            <div class="video-viewer-content">
                <video id="video-player" controls></video>
            </div>
        </div>
    </div>

    <!-- 相册相关弹窗 -->
    <!-- 创建/编辑相册弹窗 -->
    <div class="album-modal" id="album-modal">
        <div class="album-modal-container">
            <div class="album-modal-header">
                <h3 id="album-modal-title">创建相册</h3>
                <button class="close-btn" onclick="hideAlbumModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="album-modal-content">
                <form id="album-form">
                    <div class="form-group">
                        <label for="album-name">相册名称</label>
                        <input type="text" id="album-name" placeholder="输入相册名称" maxlength="50" required>
                    </div>
                    <div class="form-group">
                        <label for="album-description">相册描述</label>
                        <textarea id="album-description" placeholder="输入相册描述（可选）" maxlength="200"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="album-cover">相册封面</label>
                        <div class="cover-upload-area">
                            <div class="cover-preview" id="cover-preview">
                                <i class="fas fa-camera"></i>
                                <span>点击选择封面图片</span>
                            </div>
                            <input type="file" id="cover-upload" accept="image/*" hidden>
                        </div>
                        <!-- 封面位置调整控件 -->
                        <div class="cover-position-control" id="cover-position-control" style="display: none;">
                            <label>封面显示位置</label>
                            <div class="position-options">
                                <button type="button" class="position-btn active" data-position="top">上部</button>
                                <button type="button" class="position-btn" data-position="center">中部</button>
                                <button type="button" class="position-btn" data-position="bottom">下部</button>
                            </div>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="cancel-btn" onclick="hideAlbumModal()">取消</button>
                        <button type="submit" class="save-btn" id="save-album-btn">
                            <span class="btn-text">创建</span>
                            <span class="btn-loading" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除相册确认弹窗 -->
    <div class="confirm-modal" id="delete-confirm-modal">
        <div class="confirm-modal-container">
            <div class="confirm-modal-content">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>确认删除</h3>
                <p>确定要删除这个相册吗？相册中的所有照片和视频也会被删除，此操作无法撤销。</p>
                <div class="confirm-actions">
                    <button class="cancel-btn" onclick="hideDeleteConfirmModal()">取消</button>
                    <button class="delete-btn" onclick="confirmDeleteAlbum()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除媒体确认弹窗 -->
    <div class="confirm-modal" id="delete-media-confirm-modal">
        <div class="confirm-modal-container">
            <div class="confirm-modal-content">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>确认删除</h3>
                <p>确定要删除这个文件吗？此操作无法撤销。</p>
                <div class="confirm-actions">
                    <button class="cancel-btn" onclick="hideDeleteMediaConfirmModal()">取消</button>
                    <button class="delete-btn" onclick="confirmDeleteMedia()">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 朋友圈删除确认弹窗 - 大厂风格设计 -->
    <div class="moments-delete-confirm-modal" id="moments-delete-confirm-modal">
        <div class="moments-delete-confirm-container">
            <div class="moments-delete-confirm-content">
                <div class="moments-delete-confirm-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h3 class="moments-delete-confirm-title">确认删除朋友圈</h3>
                <p class="moments-delete-confirm-message">确定要删除这条朋友圈吗？删除后无法恢复，请谨慎操作。</p>
                <div class="moments-delete-confirm-actions">
                    <button class="moments-delete-cancel-btn" onclick="cancelDeleteMoment()">
                        <i class="fas fa-times"></i>
                        取消
                    </button>
                    <button class="moments-delete-confirm-btn" onclick="confirmDeleteMoment()">
                        <i class="fas fa-trash"></i>
                        删除
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除好友确认弹窗 - 大厂风格设计 -->
    <div class="friend-delete-confirm-modal" id="friend-delete-confirm-modal">
        <div class="friend-delete-confirm-container">
            <div class="friend-delete-confirm-content">
                <div class="friend-delete-confirm-icon">
                    <i class="fas fa-user-times"></i>
                </div>
                <h3 class="friend-delete-confirm-title">确认删除好友</h3>
                <p class="friend-delete-confirm-message">确定要删除好友 "<span id="friend-delete-name"></span>" 吗？删除后将无法查看对方朋友圈，请谨慎操作。</p>
                <div class="friend-delete-confirm-actions">
                    <button class="friend-delete-cancel-btn" onclick="cancelDeleteFriend()">
                        <i class="fas fa-times"></i>
                        取消
                    </button>
                    <button class="friend-delete-confirm-btn" onclick="confirmDeleteFriendModal()">
                        <i class="fas fa-user-minus"></i>
                        删除好友
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 退出登录加载弹窗 - 大厂风格设计 -->
    <div class="logout-loading-modal" id="logout-loading-modal">
        <div class="logout-loading-container">
            <div class="logout-loading-content">
                <div class="logout-loading-icon">
                    <i class="fas fa-sign-out-alt"></i>
                </div>
                <h3 class="logout-loading-title">正在退出登录</h3>
                <p class="logout-loading-message">请稍候，正在为您安全退出...</p>
                <div class="logout-loading-spinner">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast提示容器 -->
    <div class="toast-container" id="toast-container">
        <!-- Toast提示将通过JavaScript动态生成 -->
    </div>

    <!-- WebSocket连接状态指示器 -->
    <div class="websocket-status disconnected" id="websocket-status" style="display: none;">
        <i class="fas fa-wifi"></i>
        <span>连接中...</span>
    </div>

    <!-- JavaScript引入 -->
    <!-- Socket.IO客户端库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <!-- 配置文件 -->
    <script src="config.js"></script>
    <!-- 公共JS -->
    <script src="js/common.js"></script>
    <script src="js/utils.js"></script>
    <!-- WebSocket管理 -->
    <script src="js/websocket.js"></script>
    <!-- 各个页面的JS -->
    <script src="js/chat.js"></script>
    <script src="js/contacts.js"></script>
    <script src="js/moments.js"></script>
    <script src="js/profile.js"></script>
    <script src="js/albums.js"></script>
    <!-- Stagewise toolbar removed -->
</body>
</html> 