<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端朋友圈优化测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .test-item {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
        }
        
        .test-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .test-result {
            color: #666;
            font-size: 14px;
        }
        
        .status-good { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        
        .scroll-test {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            touch-action: pan-y;
            -webkit-overflow-scrolling: touch;
        }
        
        .scroll-item {
            height: 60px;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #007AFF, #5AC8FA);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .lazy-image {
            width: 100%;
            height: 150px;
            background: #eee;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            margin: 10px 0;
            position: relative;
        }
        
        .lazy-image.loading::before {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>移动端朋友圈优化测试</h2>
        
        <div class="test-item">
            <div class="test-title">设备检测</div>
            <div class="test-result" id="device-info">检测中...</div>
        </div>
        
        <div class="test-item">
            <div class="test-title">触摸支持</div>
            <div class="test-result" id="touch-info">检测中...</div>
        </div>
        
        <div class="test-item">
            <div class="test-title">滑动测试</div>
            <div class="test-result">请在下方区域测试垂直滑动</div>
            <div class="scroll-test" id="scroll-test">
                <div class="scroll-item">滑动项目 1</div>
                <div class="scroll-item">滑动项目 2</div>
                <div class="scroll-item">滑动项目 3</div>
                <div class="scroll-item">滑动项目 4</div>
                <div class="scroll-item">滑动项目 5</div>
                <div class="scroll-item">滑动项目 6</div>
                <div class="scroll-item">滑动项目 7</div>
                <div class="scroll-item">滑动项目 8</div>
            </div>
            <div class="test-result" id="scroll-result">等待滑动测试...</div>
        </div>
        
        <div class="test-item">
            <div class="test-title">懒加载测试</div>
            <div class="test-result">模拟图片懒加载效果</div>
            <div class="lazy-image loading" id="lazy-test">加载中...</div>
            <div class="test-result" id="lazy-result">等待加载...</div>
        </div>
        
        <div class="test-item">
            <div class="test-title">性能指标</div>
            <div class="test-result" id="performance-info">计算中...</div>
        </div>
    </div>

    <script>
        // 设备检测
        const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        const hasTouch = 'ontouchstart' in window;
        const maxTouchPoints = navigator.maxTouchPoints || 0;
        
        document.getElementById('device-info').innerHTML = `
            <span class="${isMobile ? 'status-good' : 'status-warning'}">
                ${isMobile ? '✓ 移动设备' : '⚠ 桌面设备'} 
                (${navigator.userAgent.split(' ')[0]})
            </span>
        `;
        
        document.getElementById('touch-info').innerHTML = `
            <span class="${hasTouch ? 'status-good' : 'status-error'}">
                ${hasTouch ? '✓ 支持触摸' : '✗ 不支持触摸'}
            </span>
            <br>触摸点数: ${maxTouchPoints}
        `;
        
        // 滑动测试
        let scrollCount = 0;
        const scrollTest = document.getElementById('scroll-test');
        const scrollResult = document.getElementById('scroll-result');
        
        scrollTest.addEventListener('scroll', () => {
            scrollCount++;
            scrollResult.innerHTML = `<span class="status-good">✓ 滑动正常 (${scrollCount} 次)</span>`;
        });
        
        // 懒加载测试
        setTimeout(() => {
            const lazyTest = document.getElementById('lazy-test');
            const lazyResult = document.getElementById('lazy-result');
            
            lazyTest.classList.remove('loading');
            lazyTest.textContent = '✓ 加载完成';
            lazyTest.style.background = 'linear-gradient(45deg, #28a745, #20c997)';
            lazyTest.style.color = 'white';
            
            lazyResult.innerHTML = '<span class="status-good">✓ 懒加载正常</span>';
        }, 2000);
        
        // 性能测试
        setTimeout(() => {
            const performanceInfo = document.getElementById('performance-info');
            const timing = performance.timing;
            const loadTime = timing.loadEventEnd - timing.navigationStart;
            
            performanceInfo.innerHTML = `
                页面加载时间: <span class="${loadTime < 1000 ? 'status-good' : 'status-warning'}">${loadTime}ms</span><br>
                内存使用: <span class="status-good">${(performance.memory?.usedJSHeapSize / 1024 / 1024).toFixed(2) || 'N/A'} MB</span>
            `;
        }, 1000);
    </script>
</body>
</html>
