<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast提示优化测试</title>
    <link rel="stylesheet" href="css/common.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
            font-weight: 600;
        }
        
        .test-section {
            margin-bottom: 30px;
        }
        
        .test-section h3 {
            color: #555;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 500;
        }
        
        .test-button {
            display: inline-block;
            margin: 8px;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: transform 0.2s ease;
            color: white;
        }
        
        .test-button:active {
            transform: scale(0.98);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .btn-error {
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%);
        }
        
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%);
        }
        
        .test-description {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <!-- Toast容器 -->
    <div id="toast-container" class="toast-container"></div>

    <div class="test-container">
        <h1 class="test-title">Toast提示优化测试</h1>
        
        <div class="test-description">
            <strong>优化内容：</strong><br>
            1. 错误提示现在会在6秒后自动消失<br>
            2. 优化了错误提示的样式，增加了动画效果<br>
            3. 点击Toast区域可以立即关闭<br>
            4. 关闭按钮更加明显<br>
            5. 鼠标悬浮有交互反馈
        </div>

        <div class="test-section">
            <h3>🎉 成功提示测试</h3>
            <button class="test-button btn-success" onclick="testSuccess()">
                显示成功提示
            </button>
            <button class="test-button btn-success" onclick="testSuccessWithTitle()">
                带标题的成功提示
            </button>
        </div>

        <div class="test-section">
            <h3>❌ 错误提示测试（重点优化）</h3>
            <button class="test-button btn-error" onclick="testError()">
                普通错误提示
            </button>
            <button class="test-button btn-error" onclick="testDeleteSelfError()">
                删除自己的错误提示
            </button>
            <button class="test-button btn-error" onclick="testNotFriendError()">
                不是好友关系错误
            </button>
            <button class="test-button btn-error" onclick="testNetworkError()">
                网络错误提示
            </button>
        </div>

        <div class="test-section">
            <h3>⚠️ 警告提示测试</h3>
            <button class="test-button btn-warning" onclick="testWarning()">
                显示警告提示
            </button>
        </div>

        <div class="test-section">
            <h3>ℹ️ 信息提示测试</h3>
            <button class="test-button btn-info" onclick="testInfo()">
                显示信息提示
            </button>
        </div>

        <div class="test-section">
            <h3>🔄 批量测试</h3>
            <button class="test-button btn-info" onclick="testMultiple()">
                显示多个提示
            </button>
            <button class="test-button btn-error" onclick="clearAllToasts()">
                清除所有提示
            </button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="js/common.js"></script>
    <script>
        // 测试函数
        function testSuccess() {
            showSuccessToast('操作成功完成！');
        }

        function testSuccessWithTitle() {
            showSuccessToast('好友添加成功，现在可以开始聊天了', '添加成功');
        }

        function testError() {
            showErrorToast('这是一个普通的错误提示，现在会在6秒后自动消失');
        }

        function testDeleteSelfError() {
            showErrorToast('不能删除自己哦！', '操作提示');
        }

        function testNotFriendError() {
            showErrorToast('该用户不在您的好友列表中', '删除失败');
        }

        function testNetworkError() {
            showErrorToast('网络连接失败，请检查网络后重试', '删除失败');
        }

        function testWarning() {
            showWarningToast('这个操作可能会影响其他功能，请谨慎操作', '警告');
        }

        function testInfo() {
            showInfoToast('这是一条信息提示，用于向用户传达重要信息');
        }

        function testMultiple() {
            showSuccessToast('第一条成功提示');
            setTimeout(() => showWarningToast('第二条警告提示'), 500);
            setTimeout(() => showErrorToast('第三条错误提示'), 1000);
            setTimeout(() => showInfoToast('第四条信息提示'), 1500);
        }

        function clearAllToasts() {
            const toasts = document.querySelectorAll('.toast');
            toasts.forEach(toast => {
                hideToast(toast);
            });
        }
    </script>
</body>
</html>
